# Enhanced IELTS System - Phase-by-Phase Development Guide

## 📊 **CURRENT IMPLEMENTATION STATUS** (Updated: December 2024)

### � **COMPLETED PHASES**
- ✅ **Phase 1**: Project Foundation (100% Complete)
- ✅ **Phase 2**: Authentication & Organization System (100% Complete)
- 🚧 **Phase 3**: Core Candidate & Test Management (60% Complete)

### 🚀 **LIVE SYSTEM FEATURES**
- ✅ Multi-role authentication (Master Admin, Org Admin, Test Checker)
- ✅ Organization management with CRUD operations
- ✅ Candidate management with validation
- ✅ Role-based dashboards and navigation
- ✅ Database with full schema and sample data
- ✅ Responsive UI with Tailwind CSS

### 🔑 **TEST CREDENTIALS**
```
Master Admin: <EMAIL> / admin123
Org Admin: <EMAIL> / password123
Test Checker: <EMAIL> / password123
```

### 🎯 **NEXT MILESTONE**
Currently implementing Phase 3: Test Registration & Result Entry System

---

## �🎯 Development Approach

Each phase builds upon the previous one, creating a fully functional system incrementally. Follow this guide step-by-step to ensure proper implementation.

---

## 📋 Phase 1: Project Foundation (Weeks 1-2)

### Week 1: Project Setup & Environment

#### Step 1.1: Initialize Project
```bash
# Create Next.js project
npx create-next-app@latest enhanced-ielts-system --typescript --tailwind --eslint --app --src-dir

# Install core dependencies
npm install @auth/drizzle-adapter @paralleldrive/cuid2 bcryptjs drizzle-orm drizzle-kit postgres next-auth@beta

# Install UI dependencies
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select @radix-ui/react-toast @radix-ui/react-tabs lucide-react

# Install form and validation
npm install react-hook-form @hookform/resolvers zod

# Install additional tools
npm install @anthropic-ai/sdk jspdf html2canvas recharts zustand @tanstack/react-query
```

#### Step 1.2: Configure Development Tools
```bash
# Install dev dependencies
npm install -D @types/bcryptjs @types/pg prettier prettier-plugin-tailwindcss husky lint-staged

# Setup Husky for git hooks
npx husky install
npx husky add .husky/pre-commit "lint-staged"
```

#### Step 1.3: Create Project Structure
```bash
# Create main directories
mkdir -p src/{components,lib,types,styles}
mkdir -p src/components/{ui,forms,paywall,results,charts,layout,specialized}
mkdir -p src/lib/{db,auth,payments,ai,certificates,promotions,utils,hooks}
mkdir -p src/app/{master,admin,checker,api}
mkdir -p public/{images,fonts}
mkdir -p scripts docs tests
```

#### Step 1.4: Setup Configuration Files
Create all configuration files (next.config.ts, tailwind.config.ts, drizzle.config.ts, etc.) as specified in the Project Structure document.

### Week 2: Database Design & Setup

#### Step 2.1: Create Database Schema
```typescript
// src/lib/db/schema.ts
import { pgTable, text, timestamp, integer, decimal, boolean, json, unique } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';

// Organizations table
export const organizations = pgTable('organizations', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  name: text('name').notNull(),
  slug: text('slug').unique().notNull(),
  settings: json('settings').$type<OrganizationSettings>().default({}),
  features: json('features').$type<string[]>().default([]),
  billingPlan: text('billing_plan').default('basic'),
  status: text('status', { enum: ['active', 'suspended', 'disabled'] }).default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Continue with all other tables...
```

#### Step 2.2: Setup Database Connection
```typescript
// src/lib/db/index.ts
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

const connectionString = process.env.DATABASE_URL!;
const client = postgres(connectionString, { prepare: false });
export const db = drizzle(client, { schema });
```

#### Step 2.3: Create Migration Scripts
```bash
# Generate initial migration
npm run db:generate

# Create seed data script
# scripts/seed-data.ts - Create initial organizations, users, etc.
```

---

## 🔐 Phase 2: Authentication & Organization System (Weeks 3-4)

### Week 3: Authentication Foundation

#### Step 3.1: Configure NextAuth.js
```typescript
// src/lib/auth/config.ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { DrizzleAdapter } from '@auth/drizzle-adapter';
import { db } from '@/lib/db';
import bcrypt from 'bcryptjs';

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: DrizzleAdapter(db),
  session: { strategy: 'jwt' },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        // Implementation for user authentication
        // Check credentials against database
        // Return user object or null
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.organizationId = user.organizationId;
        token.masterAdmin = user.masterAdmin;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.role = token.role as string;
      session.user.organizationId = token.organizationId as string;
      session.user.masterAdmin = token.masterAdmin as boolean;
      return session;
    },
  },
});
```

#### Step 3.2: Create Authentication Pages
```typescript
// src/app/(auth)/login/page.tsx
'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.ok) {
        // Redirect based on user role
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <form onSubmit={handleSubmit} className="space-y-4 w-full max-w-md">
        <Input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
        <Input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? 'Signing in...' : 'Sign In'}
        </Button>
      </form>
    </div>
  );
}
```

#### Step 3.3: Create Middleware for Route Protection
```typescript
// src/middleware.ts
import { auth } from '@/lib/auth/config';
import { NextResponse } from 'next/server';

export default auth((req) => {
  const { pathname } = req.nextUrl;
  const user = req.auth?.user;

  // Public routes
  const publicRoutes = ['/', '/login', '/search', '/results', '/verify'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Require authentication for protected routes
  if (!user) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Role-based access control
  if (pathname.startsWith('/master') && !user.masterAdmin) {
    return NextResponse.redirect(new URL('/admin', req.url));
  }

  if (pathname.startsWith('/admin') && user.role !== 'admin' && !user.masterAdmin) {
    return NextResponse.redirect(new URL('/checker', req.url));
  }

  return NextResponse.next();
});

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
```

### Week 4: Organization Management

#### Step 4.1: Create Master Admin Dashboard
```typescript
// src/app/master/dashboard/page.tsx
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { organizations } from '@/lib/db/schema';
import { OrganizationCard } from '@/components/specialized/organization-card';

export default async function MasterDashboardPage() {
  const session = await auth();

  if (!session?.user?.masterAdmin) {
    throw new Error('Unauthorized');
  }

  const orgs = await db.select().from(organizations);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Master Dashboard</h1>
        <Button>Create Organization</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {orgs.map((org) => (
          <OrganizationCard key={org.id} organization={org} />
        ))}
      </div>
    </div>
  );
}
```

#### Step 4.2: Create Organization CRUD Operations
```typescript
// src/app/api/master/organizations/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { organizations, users } from '@/lib/db/schema';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.masterAdmin) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { name, slug, adminEmail, adminPassword, features } = await request.json();

  try {
    // Create organization
    const [org] = await db.insert(organizations).values({
      name,
      slug,
      features,
    }).returning();

    // Create organization admin
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    await db.insert(users).values({
      organizationId: org.id,
      email: adminEmail,
      password: hashedPassword,
      role: 'admin',
      name: `${name} Admin`,
    });

    return NextResponse.json(org, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 });
  }
}
```

---

## 👤 Phase 3: Core Candidate & Test Management (Weeks 5-6)

### Week 5: Candidate Management

#### Step 5.1: Create Candidate Registration Form
```typescript
// src/components/forms/candidate-form.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FileUpload } from '@/components/ui/file-upload';

const candidateSchema = z.object({
  fullName: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  dateOfBirth: z.string(),
  nationality: z.string().min(2, 'Nationality is required'),
  passportNumber: z.string().min(5, 'Passport/Birth certificate number is required'),
  photo: z.instanceof(File).optional(),
  studentStatus: z.boolean().default(false),
});

type CandidateFormData = z.infer<typeof candidateSchema>;

interface CandidateFormProps {
  onSubmit: (data: CandidateFormData) => Promise<void>;
  initialData?: Partial<CandidateFormData>;
}

export function CandidateForm({ onSubmit, initialData }: CandidateFormProps) {
  const form = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema),
    defaultValues: initialData,
  });

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <Input
        {...form.register('fullName')}
        placeholder="Full Name"
        error={form.formState.errors.fullName?.message}
      />

      <Input
        {...form.register('email')}
        type="email"
        placeholder="Email"
        error={form.formState.errors.email?.message}
      />

      <Input
        {...form.register('phoneNumber')}
        placeholder="Phone Number"
        error={form.formState.errors.phoneNumber?.message}
      />

      <Input
        {...form.register('dateOfBirth')}
        type="date"
        placeholder="Date of Birth"
        error={form.formState.errors.dateOfBirth?.message}
      />

      <Input
        {...form.register('nationality')}
        placeholder="Nationality"
        error={form.formState.errors.nationality?.message}
      />

      <Input
        {...form.register('passportNumber')}
        placeholder="Passport/Birth Certificate Number"
        error={form.formState.errors.passportNumber?.message}
      />

      <FileUpload
        {...form.register('photo')}
        accept="image/*"
        label="Candidate Photo"
      />

      <label className="flex items-center space-x-2">
        <input
          {...form.register('studentStatus')}
          type="checkbox"
          className="rounded"
        />
        <span>Student of this test center</span>
      </label>

      <Button type="submit" disabled={form.formState.isSubmitting}>
        {form.formState.isSubmitting ? 'Saving...' : 'Save Candidate'}
      </Button>
    </form>
  );
}
```

#### Step 5.2: Create Candidate API Endpoints
```typescript
// src/app/api/candidates/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const data = await request.json();

  try {
    // Check for existing candidate with same passport number
    const existing = await db
      .select()
      .from(candidates)
      .where(eq(candidates.passportNumber, data.passportNumber))
      .limit(1);

    if (existing.length > 0) {
      return NextResponse.json(
        { error: 'Candidate with this passport/birth certificate number already exists' },
        { status: 409 }
      );
    }

    // Create new candidate
    const [candidate] = await db.insert(candidates).values({
      ...data,
      organizationId: session.user.organizationId,
    }).returning();

    return NextResponse.json(candidate, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create candidate' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const search = searchParams.get('search');
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');

  try {
    let query = db
      .select()
      .from(candidates)
      .where(eq(candidates.organizationId, session.user.organizationId));

    if (search) {
      query = query.where(
        or(
          ilike(candidates.fullName, `%${search}%`),
          eq(candidates.passportNumber, search)
        )
      );
    }

    const results = await query
      .limit(limit)
      .offset((page - 1) * limit);

    return NextResponse.json(results);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch candidates' }, { status: 500 });
  }
}
```

### Week 6: Test Results System

#### Step 6.1: Create Test Registration System
```typescript
// src/components/forms/test-registration-form.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const testRegistrationSchema = z.object({
  candidateId: z.string(),
  testDate: z.string(),
  testCenter: z.string(),
  candidateNumber: z.string(),
});

type TestRegistrationData = z.infer<typeof testRegistrationSchema>;

export function TestRegistrationForm({ onSubmit }: { onSubmit: (data: TestRegistrationData) => Promise<void> }) {
  const form = useForm<TestRegistrationData>({
    resolver: zodResolver(testRegistrationSchema),
  });

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      {/* Form fields for test registration */}
    </form>
  );
}
```

#### Step 6.2: Create Test Results Entry Interface
```typescript
// src/components/forms/result-entry-form.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const testResultSchema = z.object({
  listeningScore: z.number().min(0).max(40),
  listeningBandScore: z.number().min(0).max(9).step(0.5),
  readingScore: z.number().min(0).max(40),
  readingBandScore: z.number().min(0).max(9).step(0.5),
  writingTask1Score: z.number().min(0).max(9).step(0.5),
  writingTask2Score: z.number().min(0).max(9).step(0.5),
  writingBandScore: z.number().min(0).max(9).step(0.5),
  speakingFluencyScore: z.number().min(0).max(9).step(0.5),
  speakingLexicalScore: z.number().min(0).max(9).step(0.5),
  speakingGrammarScore: z.number().min(0).max(9).step(0.5),
  speakingPronunciationScore: z.number().min(0).max(9).step(0.5),
  speakingBandScore: z.number().min(0).max(9).step(0.5),
  overallBandScore: z.number().min(0).max(9).step(0.5),
});

type TestResultData = z.infer<typeof testResultSchema>;

export function ResultEntryForm({ onSubmit }: { onSubmit: (data: TestResultData) => Promise<void> }) {
  const form = useForm<TestResultData>({
    resolver: zodResolver(testResultSchema),
  });

  // Auto-calculate overall band score
  const watchedScores = form.watch(['listeningBandScore', 'readingBandScore', 'writingBandScore', 'speakingBandScore']);

  useEffect(() => {
    const [listening, reading, writing, speaking] = watchedScores;
    if (listening && reading && writing && speaking) {
      const overall = Math.round(((listening + reading + writing + speaking) / 4) * 2) / 2;
      form.setValue('overallBandScore', overall);
    }
  }, [watchedScores, form]);

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Listening Section */}
      <div className="grid grid-cols-2 gap-4">
        <Input
          {...form.register('listeningScore', { valueAsNumber: true })}
          type="number"
          placeholder="Listening Raw Score (0-40)"
          min="0"
          max="40"
        />
        <Input
          {...form.register('listeningBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Listening Band Score"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Reading Section */}
      <div className="grid grid-cols-2 gap-4">
        <Input
          {...form.register('readingScore', { valueAsNumber: true })}
          type="number"
          placeholder="Reading Raw Score (0-40)"
          min="0"
          max="40"
        />
        <Input
          {...form.register('readingBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Reading Band Score"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Writing Section */}
      <div className="grid grid-cols-3 gap-4">
        <Input
          {...form.register('writingTask1Score', { valueAsNumber: true })}
          type="number"
          placeholder="Writing Task 1"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('writingTask2Score', { valueAsNumber: true })}
          type="number"
          placeholder="Writing Task 2"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('writingBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Writing Band Score"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Speaking Section */}
      <div className="grid grid-cols-5 gap-4">
        <Input
          {...form.register('speakingFluencyScore', { valueAsNumber: true })}
          type="number"
          placeholder="Fluency"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingLexicalScore', { valueAsNumber: true })}
          type="number"
          placeholder="Lexical"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingGrammarScore', { valueAsNumber: true })}
          type="number"
          placeholder="Grammar"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingPronunciationScore', { valueAsNumber: true })}
          type="number"
          placeholder="Pronunciation"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Speaking Band"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Overall Score */}
      <div>
        <Input
          {...form.register('overallBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Overall Band Score"
          min="0"
          max="9"
          step="0.5"
          readOnly
          className="bg-gray-100"
        />
      </div>

      <Button type="submit" disabled={form.formState.isSubmitting}>
        {form.formState.isSubmitting ? 'Saving...' : 'Save Results'}
      </Button>
    </form>
  );
}
```

This phase-by-phase guide provides detailed implementation steps for building the Enhanced IELTS System from scratch. Each phase includes specific code examples, file structures, and implementation details to guide the development process systematically.
